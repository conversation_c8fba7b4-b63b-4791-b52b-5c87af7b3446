'use client'

import { useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useSidebarStore } from '@/stores'

interface SidebarProps {
  children?: React.ReactNode
}

export function Sidebar({ children }: SidebarProps = {}) {
  const { isOpen, setIsOpen, navigationItems, footerItems } = useSidebarStore()
  const pathname = usePathname()
  const drawerToggleRef = useRef<HTMLInputElement>(null)

  // Sync Zustand state with drawer toggle checkbox
  useEffect(() => {
    if (drawerToggleRef.current) {
      drawerToggleRef.current.checked = isOpen
    }
  }, [isOpen])

  // Handle drawer toggle change (for DaisyUI integration)
  const handleDrawerToggle = useCallback(() => {
    setIsOpen(!isOpen)
  }, [isOpen, setIsOpen])

  const handleNavigationItemClick = () => {
    setIsOpen(false) // Always close on navigation - simpler UX
  }

  const handleFooterItemClick = () => {
    setIsOpen(false) // Always close on navigation - simpler UX
  }

  const handleOverlayClick = () => {
    setIsOpen(false)
  }

  return (
    <div className="drawer lg:drawer-open">
      <input
        ref={drawerToggleRef}
        id="sidebar-drawer"
        type="checkbox"
        className="drawer-toggle"
        checked={isOpen}
        onChange={handleDrawerToggle}
        aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
      />

      <div className="drawer-content">{children}</div>

      <div className="drawer-side z-50">
        <label
          htmlFor="sidebar-drawer"
          className="drawer-overlay"
          onClick={handleOverlayClick}
          aria-label="Close sidebar"
        />
        <aside
          className="w-[var(--layout-sidebar-width)] bg-[var(--layout-sidebar-background)] flex flex-col rounded-box ms-4 my-4 min-h-[calc(100vh-2rem)]"
          aria-label="Main sidebar"
          aria-hidden={!isOpen}
          role="navigation"
        >
          {/* Header */}
          <div className="flex min-h-16 items-center justify-center px-4">
            <Link href="/" aria-label="Go to homepage">
              <Image src="/logo-light.svg" alt="Logo" width={103} height={20} priority />
            </Link>
          </div>
          <nav className="flex-1 min-h-0 overflow-y-auto px-4" aria-label="Main navigation">
            <ul className="space-y-1" role="list">
              {navigationItems.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.id}>
                    <Link
                      href={item.href}
                      onClick={handleNavigationItemClick}
                      className={`
                      flex items-center gap-3 px-4 py-2 rounded-box transition-all duration-300 hover:bg-base-200
                      ${
                        isActive
                          ? 'bg-primary text-primary-content hover:bg-primary/90'
                          : 'text-base-content hover:text-base-content'
                      }
                    `}
                      aria-current={isActive ? 'page' : undefined}
                    >
                      <span className={`${item.icon} flex-shrink-0`} aria-hidden="true" />
                      <span className="font-medium text-sm truncate">{item.label}</span>
                      {item.badge && (
                        <span
                          className={`
                          ml-auto px-2 py-1 text-xs font-semibold rounded-box flex-shrink-0
                          ${
                            isActive
                              ? 'bg-primary-content/20 text-primary-content'
                              : 'bg-primary text-primary-content'
                          }
                        `}
                          aria-label={`${item.label} has ${item.badge} ${
                            typeof item.badge === 'number' ? 'items' : ''
                          }`}
                        >
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>
          <div className="px-4 py-2 border-t border-base-300">
            <nav aria-label="Secondary navigation">
              <ul className="space-y-1" role="list">
                {footerItems.map((item) => (
                  <li key={item.id}>
                    <Link
                      href={item.href}
                      onClick={handleFooterItemClick}
                      className="flex items-center gap-3 px-4 py-2 rounded-box transition-all duration-200 text-base-content/70 hover:text-base-content hover:bg-base-200"
                    >
                      <span className={`${item.icon} flex-shrink-0`} aria-hidden="true" />
                      <span className="font-medium text-sm truncate">{item.label}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
          </div>
        </aside>
      </div>
    </div>
  )
}
