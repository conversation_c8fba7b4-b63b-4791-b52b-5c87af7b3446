'use client'

import { Theme<PERSON><PERSON>roller, UserProfile, Sidebar } from '@/components'

export default function DashboardPage() {
  return (
    <div className="size-full">
      <Sidebar>
        {/* Main content area */}
        <div className="flex h-screen min-w-0 flex-col overflow-auto">
          {/* NAVBAR */}
          <div
            role="navigation"
            aria-label="Navbar"
            className="flex items-center justify-between px-3 bg-[var(--layout-topbar-background)] z-30 h-16 static duration-300 transition-colors mx-4 mt-4 rounded-box"
          >
            <div className="inline-flex items-center gap-3">
              <label
                htmlFor="sidebar-drawer"
                className="btn btn-square btn-ghost btn-sm lg:hidden"
                aria-label="Toggle sidebar"
              >
                <span className="icon-[solar--hamburger-menu-line-duotone]"></span>
              </label>
              <button className="btn btn-outline btn-sm btn-ghost border-base-300 text-base-content/70 hidden h-9 w-48 justify-start gap-2 text-sm md:flex">
                <span className="icon-[solar--magnifer-outline] size-4"></span>
                <span>Search</span>
              </button>
            </div>
            <div className="inline-flex items-center gap-1.5">
              <ThemeController />
              <UserProfile />
            </div>
          </div>
        </div>
      </Sidebar>
    </div>
  )
}
